<!--
 * @Description: 
 * @Autor: panmy
 * @Date: 2025-07-02 14:45:18
 * @LastEditors: panmy
 * @LastEditTime: 2025-08-04 11:18:29
-->
<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="getTitle" showOkBtn okText="保存" cancelText="关闭" @ok="handleSubmit">
    <BasicForm @register="registerForm" />
  </BasicModal>
</template>
<script lang="ts" setup>
  import { ref, computed } from 'vue';
  import { BasicModal, useModalInner } from '@/components/Modal';
  import { BasicForm, useForm } from '@/components/Form';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import dayjs from 'dayjs';

  const emit = defineEmits(['register', 'reload']);

  // 初始化API和消息提示
  const api = useBaseApi('/api/knsMzpy');
  const { createMessage } = useMessage();

  const [registerForm, { setFieldsValue, resetFields, validate }] = useForm({
    schemas: [
      {
        field: 'dwdm',
        label: '院系',
        component: 'Select',
        componentProps: { placeholder: '请选择', options: [] },
        rules: [{ required: true, trigger: 'blur', message: '请选择院系' }],
      },
      {
        field: 'bjdm',
        label: '班级',
        component: 'Select',
        componentProps: { placeholder: '请选择', options: [] },
        rules: [{ required: true, trigger: 'blur', message: '请选择班级' }],
      },
      {
        field: 'xn',
        label: '学年',
        component: 'Select',
        componentProps: { placeholder: '请选择', options: [] },
        rules: [{ required: true, trigger: 'blur', message: '请选择学年' }],
      },
      {
        field: 'sbrxm',
        label: '上报人姓名',
        component: 'Input',
        componentProps: { placeholder: '请输入' },
        rules: [{ required: true, trigger: 'blur', message: '请输入上报人姓名' }],
      },
      {
        field: 'sbrgh',
        label: '上报人工号',
        component: 'Input',
        componentProps: { placeholder: '请输入' },
        rules: [{ required: true, trigger: 'blur', message: '请输入上报人工号' }],
      },
      {
        field: 'mzpysj',
        label: '民主评议时间',
        component: 'DatePicker',
        componentProps: { placeholder: '请选择', format: 'YYYY-MM-DD' },
        rules: [{ required: true, trigger: 'blur', message: '请选择民主评议时间' }],
      },
      {
        field: 'cyrs',
        label: '参与人数',
        component: 'InputNumber',
        componentProps: { placeholder: '请输入', min: 0 },
        rules: [{ required: true, trigger: 'blur', message: '请输入参与人数' }],
      },
      {
        field: 'dwlx',
        label: '单位类型',
        component: 'Select',
        componentProps: { placeholder: '请选择', options: [] },
      },
      {
        field: 'pyzj',
        label: '评议总结',
        component: 'Textarea',
        componentProps: { placeholder: '请输入', rows: 4 },
      },
      {
        field: 'fj',
        label: '附件',
        component: 'UploadFile',
        componentProps: { maxSize: 10 },
      },
    ],
    labelWidth: 120,
  });
  const [registerModal, { closeModal, changeLoading, changeOkLoading }] = useModalInner(init);
  const id = ref('');
  const getTitle = computed(() => (id.value ? '编辑' : '新建'));

  async function init(data) {
    changeLoading(true);
    resetFields();
    id.value = data?.id || '';
    if (id.value) {
      try {
        const { data: detailData } = await api.getDetail(id.value);

        // 数据转换处理
        const formData = {
          ...detailData,
          // 日期格式化处理
          mzpysj: detailData.mzpysj ? dayjs(detailData.mzpysj) : undefined,
          // 数值字段转换
          cyrs: Number(detailData.cyrs || 0),
        };

        setFieldsValue(formData);
      } catch (error) {
        console.error('获取详情失败:', error);
        createMessage.error('获取详情失败');
      }
    }
    changeLoading(false);
  }
  async function handleSubmit() {
    const values = await validate();
    if (!values) return;

    changeOkLoading(true);
    try {
      // 数据转换处理
      const submitData = {
        ...values,
        id: id.value,
        // 日期格式化处理
        mzpysj: values.mzpysj ? dayjs(values.mzpysj).format('YYYY-MM-DD') : undefined,
        // 数值处理
        cyrs: Number(values.cyrs || 0),
      };

      const res = id.value ? await api.edit({ data: submitData }) : await api.save({ data: submitData });

      createMessage.success(res.message || '保存成功');
      closeModal();
      emit('reload');
    } catch (error) {
      console.error('保存失败:', error);
      createMessage.error('保存失败');
    } finally {
      changeOkLoading(false);
    }
  }
</script>
