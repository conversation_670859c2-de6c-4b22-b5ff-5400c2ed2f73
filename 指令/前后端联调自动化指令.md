# 前后端联调自动化指令

## 概述
本指令专门用于 `questionManage` 模块的前后端接口联调自动化，基于现有的 `useBaseApi` Hook 和项目结构，实现快速、准确的接口集成。

## 使用方式

### 1. 提供接口信息
请按以下格式提供接口信息：

```markdown
## 接口基本信息
- **接口名称**: [功能描述，如：获取问卷列表]
- **接口路径**: [完整路径，如：/api/knsDcwj/getList]
- **请求方法**: [GET/POST/PUT/DELETE]
- **目标文件**: [要更新的文件路径，如：src/views/questionManage/index.vue]
- **调用方法**: [对应的方法名，如：reload、handleAdd、handleDelete]

## 请求参数
| 字段名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| pageSize | number | 否 | 10 | 每页数量 |
| currentPage | number | 否 | 1 | 当前页码 |
| keyword | string | 否 | '' | 搜索关键词 |

## 响应数据结构
```json
{
  "code": 200,
  "message": "操作成功", 
  "data": {
    "list": [
      {
        "id": "string",
        "wjmc": "string", // 问卷名称
        "zf": "number",   // 总分数
        "txkssj": "string", // 填写开始时间
        "txjssj": "string", // 填写结束时间
        "sfsy": "string"    // 是否使用 (需转换为布尔值)
      }
    ],
    "pagination": {
      "total": 0,
      "currentPage": 1,
      "pageSize": 10
    }
  }
}
```

## 特殊字段转换说明
| 后端字段 | 前端处理 | 转换逻辑 |
|----------|----------|----------|
| sfsy | Boolean | `Boolean(Number(item.sfsy \|\| 0))` |
| txkssj/txjssj | dayjs格式化 | `dayjs(item.txkssj).format('YYYY-MM-DD')` |
| zf | Number | `Number(data.zf \|\| 0)` |
```

### 2. 执行联调指令

```bash
@augment 根据上述接口信息，按照以下步骤执行联调：
1. 字段核对和差异分析
2. 更新目标文件中的接口调用
3. 处理特殊字段转换
4. 验证代码语法和逻辑
```

## 自动执行流程

### 步骤1: 字段核对分析
- 对比接口文档字段与现有代码中的字段使用
- 识别新增、修改、删除的字段
- 检查字段类型和转换需求
- 生成字段差异报告

### 步骤2: 接口调用更新
基于现有的 `useBaseApi` 模式更新接口调用：

```typescript
// 现有模式 (保持不变)
const api = useBaseApi('/api/knsDcwj');

// 标准调用方式
const { data: { list } } = await api.getList({ 
  params: { pageSize: 9999, currentPage: 1, keyword: keyword.value } 
});

// 特殊接口调用 (使用 request 方法)
const { msg } = await api.request('put', `/api/knsDcwj/editByZt/${record.id}`, { 
  params: { sfsy: record.sfsy ? 1 : 0 }, 
  isFullPath: true 
});
```

### 步骤3: 数据转换处理
自动添加或更新数据转换逻辑：

```typescript
// 列表数据转换
list.forEach(item => {
  item.sfsy = Boolean(Number(item.sfsy || 0));
});

// 详情数据转换  
setFieldsValue({
  ...data,
  zf: Number(data.zf || 0),
  sfsy: Number(data.sfsy || 0),
});

// 日期格式化
data.dateRange = [dayjs(data.txkssj), dayjs(data.txjssj)];
```

### 步骤4: 错误处理和验证
- 添加 try-catch 错误处理
- 验证必填字段
- 检查响应数据结构
- 确保类型安全

## 代码模板

### questionManage/index.vue 模板
```vue
<script lang="ts" setup>
import { useBaseApi } from '@/hooks/web/useBaseApi';

const api = useBaseApi('/api/knsDcwj');

// 列表查询
async function reload() {
  loading.value = true;
  try {
    const { data: { list } } = await api.getList({ 
      params: { 
        pageSize: 9999, 
        currentPage: 1, 
        keyword: keyword.value 
      } 
    });
    
    // 数据转换
    list.forEach(item => {
      item.sfsy = Boolean(Number(item.sfsy || 0));
    });
    
    batchList.value = list || [];
  } catch (error) {
    console.error('获取列表失败:', error);
  } finally {
    loading.value = false;
  }
}

// 删除操作
async function handleDelete(record) {
  try {
    const { msg } = await api.remove({ params: { id: record.id } });
    createMessage.success(msg);
    reload();
  } catch (error) {
    console.error('删除失败:', error);
  }
}

// 状态切换
async function handleEnable(record) {
  try {
    const { msg } = await api.request('put', `/api/knsDcwj/editByZt/${record.id}`, { 
      params: { sfsy: record.sfsy ? 1 : 0 }, 
      isFullPath: true 
    });
    createMessage.success(msg);
    reload();
  } catch (error) {
    console.error('状态更新失败:', error);
  }
}
</script>
```

### questionManage/components/AddForm.vue 模板
```vue
<script lang="ts" setup>
import { useBaseApi } from '@/hooks/web/useBaseApi';

const api = useBaseApi('/api/knsDcwj');

// 获取详情
async function getDetail(data) {
  spinning.value = true;
  try {
    if (id.value) {
      const { data } = await api.getDetail(toRaw(props.stepIds[0]));
      data.dateRange = [dayjs(data.txkssj), dayjs(data.txjssj)];
      setFieldsValue({
        ...data,
        zf: Number(data.zf || 0),
        sfsy: Number(data.sfsy || 0),
      });
    }
  } catch (error) {
    console.error('获取详情失败:', error);
  } finally {
    spinning.value = false;
  }
}

// 保存数据
async function save() {
  try {
    const values = await validate();
    if (!values) return;
    
    // 日期处理
    if (values.dateRange) {
      values.txkssj = dayjs(values.dateRange[0]).format('YYYY-MM-DD 00:00:00');
      values.txjssj = dayjs(values.dateRange[1]).format('YYYY-MM-DD 23:59:59');
    }
    
    const query = {
      ...values,
      id: id.value,
      sfsy: values.sfsy ? 1 : 0,
    };

    const res = id.value
      ? await api.edit({ data: query, requestOptions: { isTransformResponse: true } })
      : await api.save({ data: query, requestOptions: { isTransformResponse: true } });
      
    if (res.code == 200) props.shareInfo.wjdm = res.data.wjdm;
    
    return {
      success: true,
      id: id.value || 'new-generated-id',
      errorMessage: '',
    };
  } catch (error) {
    console.error('保存失败:', error);
    return {
      success: false,
      errorMessage: error.message || '保存失败',
    };
  }
}
</script>
```

## 常见字段转换规则

### 布尔值转换
```typescript
// 后端返回字符串 "0"/"1"，前端需要布尔值
item.sfsy = Boolean(Number(item.sfsy || 0));

// 前端布尔值，后端需要数字
params: { sfsy: record.sfsy ? 1 : 0 }
```

### 日期时间转换
```typescript
// 显示格式化
dayjs(item.txkssj).format('YYYY-MM-DD')

// 提交格式化
txkssj: dayjs(values.dateRange[0]).format('YYYY-MM-DD 00:00:00')
```

### 数值转换
```typescript
// 确保数值类型
zf: Number(data.zf || 0)
```

## 执行检查清单

### 字段核对
- [ ] 请求参数字段完整性
- [ ] 响应数据字段完整性  
- [ ] 字段类型转换正确性
- [ ] 特殊字段处理逻辑

### 接口调用
- [ ] useBaseApi 初始化正确
- [ ] 接口路径和方法正确
- [ ] 参数传递格式正确
- [ ] 错误处理完整

### 数据处理
- [ ] 响应数据结构解析正确
- [ ] 字段转换逻辑正确
- [ ] 日期格式化处理
- [ ] 空值和默认值处理

### 代码质量
- [ ] TypeScript 类型安全
- [ ] 错误处理机制完善
- [ ] 代码风格一致
- [ ] 性能优化合理

## 使用示例

### 示例1: 新增接口联调
```markdown
## 接口信息
- 接口名称: 获取问卷统计数据
- 接口路径: /api/knsDcwj/getStatistics  
- 请求方法: GET
- 目标文件: src/views/questionManage/index.vue
- 调用方法: loadStatistics

## 请求参数
| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| wjdm | string | 是 | 问卷代码 |
| dateRange | array | 否 | 时间范围 |

## 响应数据
{
  "code": 200,
  "data": {
    "totalCount": 100,
    "completedCount": 80,
    "completionRate": "80%"
  }
}
```

### 示例2: 更新现有接口
```markdown
## 更新说明
- 目标文件: src/views/questionManage/components/AddForm.vue
- 更新接口: 问卷保存接口
- 新增字段: dctx (是否多次填写)
- 字段转换: dctx 需要布尔值转数字
```

执行命令：
```bash
@augment 根据上述接口信息执行联调，重点处理字段转换和错误处理
```
