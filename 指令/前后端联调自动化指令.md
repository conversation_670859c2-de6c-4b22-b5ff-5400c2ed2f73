# 前后端联调自动化指令

## 概述

本指令用于整个系统的前后端接口联调自动化，基于现有的 `useBaseApi` Hook 和项目架构，实现快速、准确的接口集成。适用于所有业务模块的接口开发和维护。

## 使用方式

### 1. 提供接口信息

请按以下格式提供接口信息：

````markdown
## 接口基本信息

- **接口名称**: [功能描述，如：获取用户列表、保存表单数据]
- **接口路径**: [完整路径，如：/api/user/getList、/api/system/save]
- **请求方法**: [GET/POST/PUT/DELETE]
- **业务模块**: [所属模块，如：userManage、systemConfig、orderManage]
- **目标文件**: [要更新的文件路径，如：src/views/user/index.vue]
- **调用方法**: [对应的方法名，如：reload、handleAdd、handleDelete、handleSubmit]

## 请求参数

| 字段名      | 类型   | 必填    | 默认值   | 说明       |
| ----------- | ------ | ------- | -------- | ---------- |
| pageSize    | number | 否      | 10       | 每页数量   |
| currentPage | number | 否      | 1        | 当前页码   |
| keyword     | string | 否      | ''       | 搜索关键词 |
| [其他字段]  | [类型] | [是/否] | [默认值] | [字段说明] |

## 响应数据结构

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 列表接口响应
    "list": [
      {
        "id": "string",
        "[字段名]": "[类型]", // [字段说明]
        "createTime": "string", // 创建时间
        "updateTime": "string" // 更新时间
      }
    ],
    "pagination": {
      "total": 0,
      "currentPage": 1,
      "pageSize": 10
    }

    // 或详情/保存接口响应
    // "id": "string",
    // "[字段名]": "[值]"
  }
}
```
````

## 特殊字段转换说明

| 后端字段 | 前端处理   | 转换逻辑   | 适用场景   |
| -------- | ---------- | ---------- | ---------- |
| [字段名] | [目标类型] | [转换代码] | [使用说明] |

### 常见转换示例：

| 后端字段    | 前端处理   | 转换逻辑                                               | 适用场景   |
| ----------- | ---------- | ------------------------------------------------------ | ---------- |
| status/sfsy | Boolean    | `Boolean(Number(item.status \|\| 0))`                  | 状态字段   |
| createTime  | 格式化显示 | `dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')` | 时间显示   |
| dateRange   | 日期范围   | `[dayjs(data.startTime), dayjs(data.endTime)]`         | 日期选择器 |
| amount      | Number     | `Number(data.amount \|\| 0)`                           | 金额数值   |

````

### 2. 执行联调指令

```bash
@augment 根据上述接口信息，按照以下步骤执行联调：
1. 字段核对和差异分析
2. 更新目标文件中的接口调用
3. 处理特殊字段转换
4. 验证代码语法和逻辑
````

## 自动执行流程

### 步骤 1: 字段核对分析

- 对比接口文档字段与现有代码中的字段使用
- 识别新增、修改、删除的字段
- 检查字段类型和转换需求
- 生成字段差异报告

### 步骤 2: 接口调用更新

基于现有的 `useBaseApi` 模式更新接口调用：

```typescript
// 现有模式 (保持不变)
const api = useBaseApi('/api/knsDcwj');

// 标准调用方式
const {
  data: { list },
} = await api.getList({
  params: { pageSize: 9999, currentPage: 1, keyword: keyword.value },
});

// 特殊接口调用 (使用 request 方法)
const { msg } = await api.request('put', `/api/knsDcwj/editByZt/${record.id}`, {
  params: { sfsy: record.sfsy ? 1 : 0 },
  isFullPath: true,
});
```

### 步骤 3: 数据转换处理

自动添加或更新数据转换逻辑：

```typescript
// 列表数据转换
list.forEach(item => {
  item.sfsy = Boolean(Number(item.sfsy || 0));
});

// 详情数据转换
setFieldsValue({
  ...data,
  zf: Number(data.zf || 0),
  sfsy: Number(data.sfsy || 0),
});

// 日期格式化
data.dateRange = [dayjs(data.txkssj), dayjs(data.txjssj)];
```

### 步骤 4: 错误处理和验证

- 添加 try-catch 错误处理
- 验证必填字段
- 检查响应数据结构
- 确保类型安全

## 代码模板

### 通用列表页面模板 (如：src/views/[module]/index.vue)

```vue
<script lang="ts" setup>
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/[modulePrefix]');

  // 列表查询
  async function reload() {
    loading.value = true;
    try {
      const {
        data: { list },
      } = await api.getList({
        params: {
          pageSize: 10,
          currentPage: 1,
          keyword: keyword.value,
        },
      });

      // 数据转换 (根据实际字段调整)
      list.forEach(item => {
        // 示例：状态字段转换
        if (item.status !== undefined) {
          item.status = Boolean(Number(item.status || 0));
        }
        // 示例：时间格式化
        if (item.createTime) {
          item.createTimeFormatted = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss');
        }
      });

      dataList.value = list || [];
    } catch (error) {
      console.error('获取列表失败:', error);
      createMessage.error('获取数据失败');
    } finally {
      loading.value = false;
    }
  }

  // 删除操作
  async function handleDelete(record) {
    createConfirm({
      title: '确认删除',
      content: '确定要删除这条记录吗？',
      onOk: async () => {
        try {
          const { msg } = await api.remove({ params: { id: record.id } });
          createMessage.success(msg || '删除成功');
          reload();
        } catch (error) {
          console.error('删除失败:', error);
          createMessage.error('删除失败');
        }
      },
    });
  }

  // 状态切换 (根据实际接口调整)
  async function handleStatusChange(record) {
    try {
      const { msg } = await api.request('put', `/api/[modulePrefix]/updateStatus/${record.id}`, {
        params: { status: record.status ? 1 : 0 },
        isFullPath: true,
      });
      createMessage.success(msg || '状态更新成功');
      reload();
    } catch (error) {
      console.error('状态更新失败:', error);
      createMessage.error('状态更新失败');
      // 回滚状态
      record.status = !record.status;
    }
  }
</script>
```

### 通用表单组件模板 (如：src/views/[module]/components/AddForm.vue)

```vue
<script lang="ts" setup>
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/[modulePrefix]');

  // 获取详情
  async function getDetail(recordId) {
    spinning.value = true;
    try {
      if (recordId) {
        const { data } = await api.getDetail(recordId);

        // 数据转换处理 (根据实际字段调整)
        const formData = {
          ...data,
          // 示例：数值字段转换
          amount: Number(data.amount || 0),
          // 示例：状态字段转换
          status: Boolean(Number(data.status || 0)),
          // 示例：日期范围处理
          dateRange: data.startTime && data.endTime ? [dayjs(data.startTime), dayjs(data.endTime)] : undefined,
        };

        setFieldsValue(formData);
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      createMessage.error('获取详情失败');
    } finally {
      spinning.value = false;
    }
  }

  // 保存数据
  async function save() {
    const values = await validate();
    if (!values) return { success: false, errorMessage: '表单验证失败' };

    // 数据转换处理 (根据实际字段调整)
    const submitData = {
      ...values,
      id: recordId.value,
      // 示例：日期范围处理
      startTime: values.dateRange ? dayjs(values.dateRange[0]).format('YYYY-MM-DD 00:00:00') : undefined,
      endTime: values.dateRange ? dayjs(values.dateRange[1]).format('YYYY-MM-DD 23:59:59') : undefined,
      // 示例：状态字段转换
      status: values.status ? 1 : 0,
      // 示例：数值处理
      amount: Number(values.amount || 0),
    };

    // 移除不需要的字段
    delete submitData.dateRange;

    const res = recordId.value ? await api.edit({ data: submitData }) : await api.save({ data: submitData });
    createMessage.success(res.message || '保存成功');
    createMessage.success('保存成功');
    closeModal();
    emit('reload');
  }
</script>
```

## 常见字段转换规则

### 布尔值转换

```typescript
// 后端返回字符串 "0"/"1"，前端需要布尔值
item.sfsy = Boolean(Number(item.sfsy || 0));

// 前端布尔值，后端需要数字
params: {
  sfsy: record.sfsy ? 1 : 0;
}
```

### 日期时间转换

```typescript
// 显示格式化
dayjs(item.txkssj).format('YYYY-MM-DD');

// 提交格式化
txkssj: dayjs(values.dateRange[0]).format('YYYY-MM-DD 00:00:00');
```

### 数值转换

```typescript
// 确保数值类型
zf: Number(data.zf || 0);
```

## 执行检查清单

### 字段核对

- [ ] 请求参数字段完整性
- [ ] 响应数据字段完整性
- [ ] 字段类型转换正确性
- [ ] 特殊字段处理逻辑

### 接口调用

- [ ] useBaseApi 初始化正确
- [ ] 接口路径和方法正确
- [ ] 参数传递格式正确
- [ ] 错误处理完整

### 数据处理

- [ ] 响应数据结构解析正确
- [ ] 字段转换逻辑正确
- [ ] 日期格式化处理
- [ ] 空值和默认值处理

### 代码质量

- [ ] TypeScript 类型安全
- [ ] 错误处理机制完善
- [ ] 代码风格一致
- [ ] 性能优化合理

## 使用示例

### 示例 1: 新增接口联调

```markdown
## 接口信息

- 接口名称: 获取用户统计数据
- 接口路径: /api/user/getStatistics
- 请求方法: GET
- 业务模块: userManage
- 目标文件: src/views/user/index.vue
- 调用方法: loadStatistics

## 请求参数

| 字段名    | 类型   | 必填 | 说明     |
| --------- | ------ | ---- | -------- |
| userId    | string | 是   | 用户 ID  |
| dateRange | array  | 否   | 时间范围 |
| status    | number | 否   | 状态筛选 |

## 响应数据

{ "code": 200, "data": { "totalCount": 100, "activeCount": 80, "inactiveCount": 20, "growthRate": "15%" } }

## 特殊字段转换

| 后端字段 | 前端处理 | 转换逻辑                              | 适用场景 |
| -------- | -------- | ------------------------------------- | -------- |
| status   | Boolean  | `Boolean(Number(item.status \|\| 0))` | 状态显示 |
```

### 示例 2: 更新现有接口

```markdown
## 更新说明

- 目标文件: src/views/order/components/AddForm.vue
- 更新接口: 订单保存接口
- 业务模块: orderManage
- 新增字段: paymentMethod (支付方式)
- 修改字段: amount 改为 totalAmount
- 字段转换: paymentMethod 需要字符串转数字
```

执行命令：

```bash
@augment 根据上述接口信息执行联调，重点处理字段转换和错误处理
```
