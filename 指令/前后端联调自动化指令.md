# 前后端联调自动化指令

## 概述

本指令用于整个系统的前后端接口联调自动化，基于现有的 `useBaseApi` Hook 和项目架构，实现快速、准确的接口集成。适用于所有业务模块的接口开发和维护。

## 使用方式

### 1. 提供接口信息

请按以下格式提供接口信息：

````markdown
## 接口基本信息

- **接口名称**: [功能描述，如：获取用户列表、保存表单数据]
- **接口路径**: [完整路径，如：/api/user/getList、/api/system/save]
- **请求方法**: [GET/POST/PUT/DELETE]
- **业务模块**: [所属模块，如：userManage、systemConfig、orderManage]
- **目标文件**: [要更新的文件路径，如：src/views/user/index.vue]
- **调用方法**: [对应的方法名，如：reload、handleAdd、handleDelete、handleSubmit]

## 请求参数

| 字段名      | 类型   | 必填    | 默认值   | 说明       |
| ----------- | ------ | ------- | -------- | ---------- |
| pageSize    | number | 否      | 10       | 每页数量   |
| currentPage | number | 否      | 1        | 当前页码   |
| keyword     | string | 否      | ''       | 搜索关键词 |
| [其他字段]  | [类型] | [是/否] | [默认值] | [字段说明] |

## 响应数据结构

```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 列表接口响应
    "list": [
      {
        "id": "string",
        "[字段名]": "[类型]", // [字段说明]
        "createTime": "string", // 创建时间
        "updateTime": "string" // 更新时间
      }
    ],
    "pagination": {
      "total": 0,
      "currentPage": 1,
      "pageSize": 10
    }

    // 或详情/保存接口响应
    // "id": "string",
    // "[字段名]": "[值]"
  }
}
```
````

## 特殊字段转换说明

| 后端字段 | 前端处理   | 转换逻辑   | 适用场景   |
| -------- | ---------- | ---------- | ---------- |
| [字段名] | [目标类型] | [转换代码] | [使用说明] |

### 常见转换示例：

| 后端字段    | 前端处理   | 转换逻辑                                               | 适用场景   |
| ----------- | ---------- | ------------------------------------------------------ | ---------- |
| status/sfsy | Boolean    | `Boolean(Number(item.status \|\| 0))`                  | 状态字段   |
| createTime  | 格式化显示 | `dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss')` | 时间显示   |
| dateRange   | 日期范围   | `[dayjs(data.startTime), dayjs(data.endTime)]`         | 日期选择器 |
| amount      | Number     | `Number(data.amount \|\| 0)`                           | 金额数值   |

````

### 2. 执行联调指令

```bash
@augment 根据上述接口信息，按照以下步骤执行联调：
1. 字段核对和差异分析
2. 更新目标文件中的接口调用
3. 处理特殊字段转换
4. 验证代码语法和逻辑
````

## 自动执行流程

### 步骤 1: 字段核对分析

- 对比接口文档字段与现有代码中的字段使用
- 识别新增、修改、删除的字段
- 检查字段类型和转换需求
- 生成字段差异报告

### 步骤 2: 接口调用更新

基于现有的 `useBaseApi` 模式更新接口调用：

```typescript
// 初始化API
const api = useBaseApi('/api/[modulePrefix]');

// 标准调用方式
const {
  data: { list },
} = await api.getList({
  params: { pageSize: 10, currentPage: 1, keyword: keyword.value },
});

// 特殊接口调用
const { msg } = await api.request('put', `/api/[modulePrefix]/updateStatus/${record.id}`, {
  params: { status: record.status ? 1 : 0 },
  isFullPath: true,
});
```

### 步骤 3: 数据转换处理

自动添加或更新数据转换逻辑（详见"常见字段转换规则"部分）：

```typescript
// 在 afterFetch 中处理列表数据转换
afterFetch: data => {
  data.forEach(item => {
    item.status = Boolean(Number(item.status || 0));
    item.createTimeFormatted = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss');
  });
  return data;
};
```

### 步骤 4: 错误处理和验证

- 添加 try-catch 错误处理
- 验证必填字段
- 检查响应数据结构
- 确保类型安全

## 支持的页面类型和功能

### 列表页面功能

- ✅ **数据列表展示** - 基于 BasicTable 组件的数据展示
- ✅ **搜索筛选** - 支持关键词搜索和多条件筛选
- ✅ **新增记录** - 打开新增表单模态框
- ✅ **编辑记录** - 行内编辑操作
- ✅ **单条删除** - 带确认的删除操作
- ✅ **批量删除** - 多选批量删除功能
- ✅ **数据导出** - Excel 导出功能
- ✅ **数据导入** - Excel 导入功能
- ✅ **分页处理** - 自动分页和数据加载
- ✅ **数据转换** - 自动处理字段类型转换

### 表单页面功能

- ✅ **新增/编辑表单** - 统一的表单组件
- ✅ **字段验证** - 表单验证规则
- ✅ **数据回显** - 编辑时数据自动填充
- ✅ **提交处理** - 新增和编辑的统一处理
- ✅ **错误处理** - 完善的错误提示机制

## 代码模板

### 通用列表页面模板 (如：src/views/[module]/index.vue)

```vue
<template>
  <div class="mtcn-content-wrapper">
    <div class="mtcn-content-wrapper-center">
      <div class="mtcn-content-wrapper-content">
        <BasicTable @register="registerTable">
          <template #tableTitle>
            <a-button type="primary" @click="handleAdd" preIcon="icon-ym icon-ym-btn-add">新增</a-button>
            <a-button type="error" color="error" @click="handleBatchDelete" preIcon="icon-ym icon-ym-delete">批量删除</a-button>
            <a-button @click="handleExport" type="link" preIcon="icon-ym icon-ym-btn-download">导出</a-button>
            <a-button @click="handleImport" type="link" preIcon="icon-ym icon-ym-btn-upload">导入</a-button>
          </template>
          <template #bodyCell="{ column, record }">
            <template v-if="column.key === 'action'">
              <TableAction :actions="getTableActions(record)" :dropDownActions="[]" />
            </template>
          </template>
        </BasicTable>

        <!-- 导入模态框 -->
        <ImportModalBrevity @register="registerImportModal" @reload="reload" />
        <!-- 导出模态框 -->
        <ExportModal @register="registerExportModal" />
        <!-- 新增/编辑模态框 -->
        <AddForm @register="registerFormModal" @reload="reload" />
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
  import { ref, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction, ActionItem } from '@/components/Table';
  import { useModal } from '@/components/Modal';
  import { useMessage } from '@/hooks/web/useMessage';
  import { useI18n } from '@/hooks/web/useI18n';
  import { ImportModalBrevity } from '@/components/CommonModal';
  import ExportModal from '@/components/CommonModal/src/ExportModal.vue';
  import AddForm from './components/AddForm.vue';
  import { useBaseApi } from '@/hooks/web/useBaseApi';
  import dayjs from 'dayjs';

  const api = useBaseApi('/api/[modulePrefix]');
  const { createMessage, createConfirm } = useMessage();
  const { t } = useI18n();

  // 表格配置
  const [registerTable, { reload, getSelectRowKeys, getFetchParams }] = useTable({
    api: params => api.getList({ params }),
    columns: [
      { title: 'ID', dataIndex: 'id', width: 80 },
      { title: '名称', dataIndex: 'name', width: 150 },
      { title: '状态', dataIndex: 'status', width: 100 },
      { title: '创建时间', dataIndex: 'createTime', width: 180 },
      // 根据实际字段调整列配置
    ],
    useSearchForm: true,
    formConfig: {
      schemas: [
        {
          field: 'keyword',
          label: t('common.keyword'),
          component: 'Input',
          componentProps: {
            placeholder: '请输入关键词搜索',
            submitOnPressEnter: true,
          },
        },
        // 根据需要添加更多搜索字段
      ],
    },
    actionColumn: {
      width: 120,
      title: '操作',
      dataIndex: 'action',
    },
    rowSelection: {
      type: 'checkbox',
    },
  });

  // 表格行操作
  function getTableActions(record): ActionItem[] {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
      {
        label: '删除',
        color: 'error',
        modelConfirm: {
          onOk: handleDelete.bind(null, record.id),
        },
      },
    ];
  }

  // 模态框注册
  const [registerFormModal, { openModal: openFormModal }] = useModal();
  const [registerImportModal, { openModal: openImportModal }] = useModal();
  const [registerExportModal, { openModal: openExportModal }] = useModal();

  // 新增
  function handleAdd() {
    openFormModal(true, { operationType: 0 });
  }

  // 编辑
  function handleEdit(record) {
    openFormModal(true, { id: record.id, operationType: 1 });
  }

  // 单条删除
  async function handleDelete(id) {
    try {
      const { msg } = await api.remove({ params: { id } });
      createMessage.success(msg || '删除成功');
      reload();
    } catch (error) {
      console.error('删除失败:', error);
      createMessage.error('删除失败');
    }
  }

  // 批量删除
  function handleBatchDelete() {
    const ids = getSelectRowKeys();
    if (!ids || ids.length === 0) {
      createMessage.warning('请选择要删除的记录');
      return;
    }
    createConfirm({
      iconType: 'warning',
      title: '提示',
      content: `确认要批量删除选中的 ${ids.length} 条记录吗？`,
      onOk: async () => {
        try {
          const { msg } = await api.batchRemove({ params: { ids: ids.join(',') } });
          createMessage.success(msg || '批量删除成功');
          reload();
        } catch (error) {
          console.error('批量删除失败:', error);
          createMessage.error('批量删除失败');
        }
      },
    });
  }

  // 导入
  function handleImport() {
    openImportModal(true, {
      type: 'position',
      isUploadDirectly: true,
      actionUrl: '/api/[modulePrefix]/import',
      downloadTemplateUrl: '/api/[modulePrefix]/downloadTemplate',
    });
  }

  // 导出
  function handleExport() {
    const listQuery = getFetchParams();
    openExportModal(true, {
      listQuery,
      exportType: '[ModuleName]Export',
      apiUrl: '/api/[modulePrefix]/export',
    });
  }

  onMounted(() => {
    reload();
  });
</script>
```

### 通用表单组件模板 (如：src/views/[module]/components/AddForm.vue)

```vue
<script lang="ts" setup>
  import { useBaseApi } from '@/hooks/web/useBaseApi';

  const api = useBaseApi('/api/[modulePrefix]');

  // 获取详情
  async function getDetail(recordId) {
    spinning.value = true;
    try {
      if (recordId) {
        const { data } = await api.getDetail(recordId);

        // 数据转换处理 (根据实际字段调整)
        const formData = {
          ...data,
          // 示例：数值字段转换
          amount: Number(data.amount || 0),
          // 示例：状态字段转换
          status: Boolean(Number(data.status || 0)),
          // 示例：日期范围处理
          dateRange: data.startTime && data.endTime ? [dayjs(data.startTime), dayjs(data.endTime)] : undefined,
        };

        setFieldsValue(formData);
      }
    } catch (error) {
      console.error('获取详情失败:', error);
      createMessage.error('获取详情失败');
    } finally {
      spinning.value = false;
    }
  }

  // 保存数据
  async function save() {
    const values = await validate();
    if (!values) return { success: false, errorMessage: '表单验证失败' };

    // 数据转换处理 (根据实际字段调整)
    const submitData = {
      ...values,
      id: recordId.value,
      // 示例：日期范围处理
      startTime: values.dateRange ? dayjs(values.dateRange[0]).format('YYYY-MM-DD 00:00:00') : undefined,
      endTime: values.dateRange ? dayjs(values.dateRange[1]).format('YYYY-MM-DD 23:59:59') : undefined,
      // 示例：状态字段转换
      status: values.status ? 1 : 0,
      // 示例：数值处理
      amount: Number(values.amount || 0),
    };

    // 移除不需要的字段
    delete submitData.dateRange;

    const res = recordId.value ? await api.edit({ data: submitData }) : await api.save({ data: submitData });
    createMessage.success(res.message || '保存成功');
    closeModal();
    emit('reload');
  }
</script>
```

## 常见字段转换规则

### 布尔值转换

```typescript
// 后端返回字符串 "0"/"1"，前端需要布尔值
item.status = Boolean(Number(item.status || 0));
item.isEnabled = Boolean(Number(item.isEnabled || 0));

// 前端布尔值，后端需要数字
params: {
  status: record.status ? 1 : 0;
}
submitData.isEnabled = values.isEnabled ? 1 : 0;
```

### 日期时间转换

```typescript
// 显示格式化
item.createTimeFormatted = dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss');
item.dateDisplay = dayjs(item.date).format('YYYY-MM-DD');

// 提交格式化
startTime: dayjs(values.dateRange[0]).format('YYYY-MM-DD 00:00:00'),
endTime: dayjs(values.dateRange[1]).format('YYYY-MM-DD 23:59:59'),

// 日期范围处理
dateRange: data.startTime && data.endTime
  ? [dayjs(data.startTime), dayjs(data.endTime)]
  : undefined
```

### 数值转换

```typescript
// 确保数值类型
amount: Number(data.amount || 0),
price: parseFloat(data.price || '0'),
count: parseInt(data.count || '0', 10)
```

### 数组和对象转换

```typescript
// 字符串转数组
tags: typeof data.tags === 'string' ? data.tags.split(',') : data.tags || [],
  // 数组转字符串
  (submitData.tags = Array.isArray(values.tags) ? values.tags.join(',') : values.tags);

// JSON字符串转对象
config: typeof data.config === 'string' ? JSON.parse(data.config || '{}') : data.config,
  // 对象转JSON字符串
  (submitData.config = typeof values.config === 'object' ? JSON.stringify(values.config) : values.config);
```

### 选择器值转换

```typescript
// 多选转换
selectedIds: Array.isArray(data.selectedIds) ? data.selectedIds : (data.selectedIds || '').split(',').filter(Boolean),

// 级联选择器
cascaderValue: data.province && data.city ? [data.province, data.city] : [],

// 提交时拆分级联值
if (values.cascaderValue && values.cascaderValue.length >= 2) {
  submitData.province = values.cascaderValue[0];
  submitData.city = values.cascaderValue[1];
}
```

## 执行检查清单

### 字段核对

- [ ] 请求参数字段完整性
- [ ] 响应数据字段完整性
- [ ] 字段类型转换正确性
- [ ] 特殊字段处理逻辑

### 接口调用

- [ ] useBaseApi 初始化正确
- [ ] 接口路径和方法正确
- [ ] 参数传递格式正确
- [ ] 错误处理完整

### 数据处理

- [ ] 响应数据结构解析正确
- [ ] 字段转换逻辑正确
- [ ] 日期格式化处理
- [ ] 空值和默认值处理

### 代码质量

- [ ] TypeScript 类型安全
- [ ] 错误处理机制完善
- [ ] 代码风格一致
- [ ] 性能优化合理

## 使用示例

### 基础使用

提供接口信息后，使用以下命令执行联调：

```bash
# 基础联调命令
@augment 根据提供的接口信息，按照 指令/前后端联调自动化指令.md 规范执行接口联调

# 列表页面联调
@augment 根据接口信息更新列表页面，包含新增、编辑、删除、批量删除、导出、导入功能

# 表单页面联调
@augment 根据接口信息更新表单组件，处理字段转换和验证规则
```

### 接口信息模板

```markdown
## 接口基本信息

- **接口名称**: 获取用户列表
- **接口路径**: /api/user/getList
- **请求方法**: GET
- **业务模块**: userManage
- **目标文件**: src/views/user/index.vue

## 请求参数

| 字段名  | 类型   | 必填 | 说明       |
| ------- | ------ | ---- | ---------- |
| keyword | string | 否   | 搜索关键词 |
| status  | number | 否   | 状态筛选   |

## 响应数据

{ "code": 200, "data": { "list": [{"id": "1", "name": "张三", "status": "1"}], "pagination": {"total": 100} } }

## 特殊字段转换

| 后端字段 | 前端处理 | 转换逻辑                              |
| -------- | -------- | ------------------------------------- |
| status   | Boolean  | `Boolean(Number(item.status \|\| 0))` |
```
